{"info": {"name": "XO Sports Hub API Collection", "description": "Complete API collection for XO Sports Hub marketplace platform", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000/api", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"mobile\": \"+1234567890\",\n  \"role\": \"seller\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mobile\": \"+1234567890\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"USER_ID_HERE\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/verify-otp", "host": ["{{baseUrl}}"], "path": ["auth", "verify-otp"]}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}}}]}, {"name": "<PERSON><PERSON> Onboarding", "item": [{"name": "<PERSON>ller Onboarding Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/seller/onboarding", "host": ["{{baseUrl}}"], "path": ["users", "seller", "onboarding"]}}, "response": []}, {"name": "Update Seller Onboarding Info", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Experienced basketball coach with 10+ years of training athletes at all levels. Specialized in shooting techniques and defensive strategies.\",\n  \"profilePic\": \"https://example.com/profile-pic.jpg\",\n  \"sports\": [\"Basketball\", \"Fitness\"],\n  \"expertise\": [\"Shooting Techniques\", \"Defense\", \"Conditioning\"],\n  \"certifications\": [\"USA Basketball Certified Coach\", \"NASM Personal Trainer\"],\n  \"experiences\": [\n    {\n      \"schoolName\": \"State University\",\n      \"position\": \"Assistant Coach\",\n      \"fromYear\": 2018,\n      \"toYear\": 2023\n    },\n    {\n      \"schoolName\": \"Local High School\",\n      \"position\": \"Head Coach\",\n      \"fromYear\": 2015,\n      \"toYear\": 2018\n    }\n  ],\n  \"minTrainingCost\": 50,\n  \"socialLinks\": {\n    \"facebook\": \"https://facebook.com/coach.john\",\n    \"linkedin\": \"https://linkedin.com/in/coach-john\",\n    \"twitter\": \"https://twitter.com/coach_john\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/users/seller/onboarding", "host": ["{{baseUrl}}"], "path": ["users", "seller", "onboarding"]}}, "response": []}, {"name": "Complete Seller Onboarding", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/users/seller/complete-onboarding", "host": ["{{baseUrl}}"], "path": ["users", "seller", "complete-onboarding"]}}, "response": []}]}, {"name": "Users", "item": [{"name": "<PERSON> Seller Profile", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/users/sellers/USER_ID_HERE", "host": ["{{baseUrl}}"], "path": ["users", "sellers", "USER_ID_HERE"]}}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"bio\": \"Professional sports trainer\"\n}"}, "url": {"raw": "{{baseUrl}}/users/profile/USER_ID_HERE", "host": ["{{baseUrl}}"], "path": ["users", "profile", "USER_ID_HERE"]}}}]}, {"name": "Content", "item": [{"name": "Get All Content", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/content", "host": ["{{baseUrl}}"], "path": ["content"]}}}, {"name": "Get Content Categories", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/content/categories", "host": ["{{baseUrl}}"], "path": ["content", "categories"]}}}, {"name": "Get Seller Content", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/content/seller/me", "host": ["{{baseUrl}}"], "path": ["content", "seller", "me"]}}}]}]}